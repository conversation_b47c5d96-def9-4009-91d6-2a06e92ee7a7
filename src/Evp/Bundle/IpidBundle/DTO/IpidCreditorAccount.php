<?php

declare(strict_types=1);

namespace Evp\Bundle\IpidBundle\DTO;

class IpidCreditorAccount
{
    private ?string $iban = null;
    private ?string $accountId = null;
    private ?string $accountHolderType = null;

    public function getIban(): ?string
    {
        return $this->iban;
    }

    public function setIban(?string $iban): self
    {
        $this->iban = $iban;
        return $this;
    }

    public function getAccountId(): ?string
    {
        return $this->accountId;
    }

    public function setAccountId(?string $accountId): self
    {
        $this->accountId = $accountId;
        return $this;
    }



    public function getAccountHolderType(): ?string
    {
        return $this->accountHolderType;
    }

    public function setAccountHolderType(?string $accountHolderType): self
    {
        $this->accountHolderType = $accountHolderType;
        return $this;
    }

    public function hasIbanOrAccountId(): bool
    {
        return $this->iban !== null || $this->accountId !== null;
    }

    public function toArray(): array
    {
        $data = [];

        if ($this->iban !== null) {
            $data['iban'] = $this->iban;
        } elseif ($this->accountId !== null) {
            $data['account_id'] = $this->accountId;
        }

        if ($this->accountHolderType !== null) {
            $data['account_holder_type'] = $this->accountHolderType;
        }

        return $data;
    }
}
