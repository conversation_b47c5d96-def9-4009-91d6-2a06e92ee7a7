<?php

declare(strict_types=1);

namespace Evp\Bundle\IpidBundle\DTO;

class IpidCreditorAccount
{
    private ?string $iban = null;
    private ?string $accountId = null;
    private ?string $currency = null;
    private ?string $accountType = null;
    private ?string $accountHolderType = null;

    public function getIban(): ?string
    {
        return $this->iban;
    }

    public function setIban(?string $iban): self
    {
        $this->iban = $iban;
        return $this;
    }

    public function getAccountId(): ?string
    {
        return $this->accountId;
    }

    public function setAccountId(?string $accountId): self
    {
        $this->accountId = $accountId;
        return $this;
    }

    public function getCurrency(): ?string
    {
        return $this->currency;
    }

    public function setCurrency(?string $currency): self
    {
        $this->currency = $currency;
        return $this;
    }

    public function getAccountType(): ?string
    {
        return $this->accountType;
    }

    public function setAccountType(?string $accountType): self
    {
        $this->accountType = $accountType;
        return $this;
    }

    public function getAccountHolderType(): ?string
    {
        return $this->accountHolderType;
    }

    public function setAccountHolderType(?string $accountHolderType): self
    {
        $this->accountHolderType = $accountHolderType;
        return $this;
    }

    public function hasIbanOrAccountId(): bool
    {
        return $this->iban !== null || $this->accountId !== null;
    }

    public function toArray(): array
    {
        $data = [];

        if ($this->iban !== null) {
            $data['iban'] = $this->iban;
        } elseif ($this->accountId !== null) {
            $data['account_id'] = $this->accountId;
        }

        if ($this->currency !== null) {
            $data['currency'] = $this->currency;
        }

        if ($this->accountType !== null) {
            $data['account_type'] = $this->accountType;
        }

        if ($this->accountHolderType !== null) {
            $data['account_holder_type'] = $this->accountHolderType;
        }

        return $data;
    }
}
