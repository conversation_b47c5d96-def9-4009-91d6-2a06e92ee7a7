<?php

declare(strict_types=1);

namespace Evp\Bundle\IpidBundle\DTO;

class IpidCreditorAgent
{
    private ?string $bic = null;
    private ?string $clearingSystemId = null;

    public function getBic(): ?string
    {
        return $this->bic;
    }

    public function setBic(?string $bic): self
    {
        $this->bic = $bic;
        return $this;
    }

    public function getClearingSystemId(): ?string
    {
        return $this->clearingSystemId;
    }

    public function setClearingSystemId(?string $clearingSystemId): self
    {
        $this->clearingSystemId = $clearingSystemId;
        return $this;
    }



    public function toArray(): array
    {
        $data = [];

        if ($this->bic !== null) {
            $data['bic'] = $this->bic;
        }

        if ($this->clearingSystemId !== null) {
            $data['clearing_system_id'] = $this->clearingSystemId;
        }

        return $data;
    }
} 