<?php

declare(strict_types=1);

namespace Evp\Bundle\IpidBundle\DTO;

class IpidValidationRequest
{
    private string $jsonPayload;
    private ?string $nodeId = null;

    public function __construct(string $jsonPayload)
    {
        $this->jsonPayload = $jsonPayload;
    }

    public function getJsonPayload(): string
    {
        return $this->jsonPayload;
    }

    public function getNodeId(): ?string
    {
        return $this->nodeId;
    }

    public function setNodeId(?string $nodeId): self
    {
        $this->nodeId = $nodeId;
        return $this;
    }

    public function getBody(): array
    {
        // For Node API, we send the JSON payload directly as the request body
        $payload = json_decode($this->jsonPayload, true, 512, JSON_THROW_ON_ERROR);

        if (!is_array($payload)) {
            throw new \InvalidArgumentException('Invalid JSON payload provided');
        }

        // Add optional node_id if specified
        if ($this->nodeId !== null) {
            $payload['node_id'] = $this->nodeId;
        }

        return $payload;
    }
} 