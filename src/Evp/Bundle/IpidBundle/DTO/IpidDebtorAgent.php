<?php

declare(strict_types=1);

namespace Evp\Bundle\IpidBundle\DTO;

class IpidDebtorAgent
{
    private ?string $bic = null;

    public function getBic(): ?string
    {
        return $this->bic;
    }

    public function setBic(?string $bic): self
    {
        $this->bic = $bic;
        return $this;
    }

    public function toArray(): array
    {
        $data = [];

        if ($this->bic !== null) {
            $data['bic'] = $this->bic;
        }

        return $data;
    }
}
