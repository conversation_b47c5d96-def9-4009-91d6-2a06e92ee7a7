<?php

declare(strict_types=1);

namespace Evp\Bundle\IpidBundle\DTO;

class IpidValidationPayload
{
    private string $countryCode;
    private IpidCreditor $creditor;
    private IpidCreditorAccount $creditorAccount;
    private ?IpidCreditorAgent $creditorAgent = null;
    private ?IpidDebtorAgent $debtorAgent = null;

    public function __construct(
        string $countryCode,
        IpidCreditor $creditor,
        IpidCreditorAccount $creditorAccount,
        ?IpidCreditorAgent $creditorAgent = null
    ) {
        $this->countryCode = $countryCode;
        $this->creditor = $creditor;
        $this->creditorAccount = $creditorAccount;
        $this->creditorAgent = $creditorAgent;
    }

    public function getCountryCode(): string
    {
        return $this->countryCode;
    }

    public function getCreditor(): IpidCreditor
    {
        return $this->creditor;
    }

    public function getCreditorAccount(): IpidCreditorAccount
    {
        return $this->creditorAccount;
    }

    public function getCreditorAgent(): ?IpidCreditorAgent
    {
        return $this->creditorAgent;
    }

    public function getDebtorAgent(): ?IpidDebtorAgent
    {
        return $this->debtorAgent;
    }

    public function setDebtorAgent(?IpidDebtorAgent $debtorAgent): self
    {
        $this->debtorAgent = $debtorAgent;
        return $this;
    }

    public function validate(): void
    {
        if (!$this->creditor->hasNameOrIdentification()) {
            throw new \InvalidArgumentException('Creditor must have either name or identification');
        }

        if (!$this->creditorAccount->hasIbanOrAccountId()) {
            throw new \InvalidArgumentException('Creditor account must have either IBAN or account_id');
        }
    }

    public function toArray(): array
    {
        $data = [
            'country_code' => $this->countryCode,
            'creditor' => $this->creditor->toArray(),
            'creditor_account' => $this->creditorAccount->toArray(),
        ];

        if ($this->creditorAgent !== null) {
            $creditorAgentData = $this->creditorAgent->toArray();
            if (!empty($creditorAgentData)) {
                $data['creditor_agent'] = $creditorAgentData;
            }
        }

        if ($this->debtorAgent !== null) {
            $debtorAgentData = $this->debtorAgent->toArray();
            if (!empty($debtorAgentData)) {
                $data['debtor_agent'] = $debtorAgentData;
            }
        }

        return $data;
    }

    public function toJson(): string
    {
        return json_encode($this->toArray(), JSON_THROW_ON_ERROR);
    }
}
