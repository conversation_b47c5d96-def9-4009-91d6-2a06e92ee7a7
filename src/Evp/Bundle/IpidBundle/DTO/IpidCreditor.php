<?php

declare(strict_types=1);

namespace Evp\Bundle\IpidBundle\DTO;

class IpidCreditor
{
    private ?string $name = null;
    private array $identification = [];

    public function getName(): ?string
    {
        return $this->name;
    }

    public function setName(?string $name): self
    {
        $this->name = $name;
        return $this;
    }



    public function getIdentification(): array
    {
        return $this->identification;
    }

    public function setIdentification(array $identification): self
    {
        $this->identification = $identification;
        return $this;
    }

    public function addIdentification(string $value, string $type): self
    {
        $this->identification[] = [
            'value' => $value,
            'type' => $type,
        ];
        return $this;
    }

    public function hasNameOrIdentification(): bool
    {
        return $this->name !== null || !empty($this->identification);
    }

    public function toArray(): array
    {
        $data = [];

        if ($this->name !== null) {
            $data['name'] = $this->name;
        }

        if (!empty($this->identification)) {
            $data['identification'] = $this->identification;
        }

        return $data;
    }
} 