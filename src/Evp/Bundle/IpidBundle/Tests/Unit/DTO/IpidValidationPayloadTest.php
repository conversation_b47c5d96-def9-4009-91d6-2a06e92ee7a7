<?php

declare(strict_types=1);

namespace Evp\Bundle\IpidBundle\Tests\Unit\DTO;

use Evp\Bundle\IpidBundle\DTO\IpidCreditor;
use Evp\Bundle\IpidBundle\DTO\IpidCreditorAccount;
use Evp\Bundle\IpidBundle\DTO\IpidCreditorAgent;
use Evp\Bundle\IpidBundle\DTO\IpidValidationPayload;
use PHPUnit\Framework\TestCase;

final class IpidValidationPayloadTest extends TestCase
{
    public function testConstructorAndGetters(): void
    {
        $creditor = new IpidCreditor();
        $creditor->setName('<PERSON>');

        $creditorAccount = new IpidCreditorAccount();
        $creditorAccount->setIban('**********************');

        $creditorAgent = new IpidCreditorAgent();
        $creditorAgent->setBic('WESTGB2L');

        $payload = new IpidValidationPayload('GB', $creditor, $creditorAccount, $creditorAgent);

        self::assertEquals('GB', $payload->getCountryCode());
        self::assertSame($creditor, $payload->getCreditor());
        self::assertSame($creditorAccount, $payload->getCreditorAccount());
        self::assertSame($creditorAgent, $payload->getCreditorAgent());
    }

    public function testValidateWithValidPayload(): void
    {
        $creditor = new IpidCreditor();
        $creditor->setName('John Smith');

        $creditorAccount = new IpidCreditorAccount();
        $creditorAccount->setIban('**********************');

        $creditorAgent = new IpidCreditorAgent();

        $payload = new IpidValidationPayload('GB', $creditor, $creditorAccount, $creditorAgent);

        // Should not throw exception
        $payload->validate();
        
        self::assertTrue(true);
    }

    public function testValidateThrowsExceptionForMissingCreditorName(): void
    {
        $creditor = new IpidCreditor();
        // No name set

        $creditorAccount = new IpidCreditorAccount();
        $creditorAccount->setIban('**********************');

        $creditorAgent = new IpidCreditorAgent();

        $payload = new IpidValidationPayload('GB', $creditor, $creditorAccount, $creditorAgent);

        $this->expectException(\InvalidArgumentException::class);
        $this->expectExceptionMessage('Creditor must have either name or identification');

        $payload->validate();
    }

    public function testValidateThrowsExceptionForMissingCreditorAccount(): void
    {
        $creditor = new IpidCreditor();
        $creditor->setName('John Smith');

        $creditorAccount = new IpidCreditorAccount();
        // No IBAN or account_id set

        $creditorAgent = new IpidCreditorAgent();

        $payload = new IpidValidationPayload('GB', $creditor, $creditorAccount, $creditorAgent);

        $this->expectException(\InvalidArgumentException::class);
        $this->expectExceptionMessage('Creditor account must have either IBAN or account_id');

        $payload->validate();
    }

    public function testToArray(): void
    {
        $creditor = new IpidCreditor();
        $creditor->setName('John Smith');

        $creditorAccount = new IpidCreditorAccount();
        $creditorAccount->setIban('**********************');

        $creditorAgent = new IpidCreditorAgent();
        $creditorAgent->setBic('WESTGB2L');

        $payload = new IpidValidationPayload('GB', $creditor, $creditorAccount, $creditorAgent);

        $expected = [
            'country_code' => 'GB',
            'creditor' => [
                'name' => 'John Smith'
            ],
            'creditor_account' => [
                'iban' => '**********************'
            ],
            'creditor_agent' => [
                'bic' => 'WESTGB2L'
            ]
        ];

        self::assertEquals($expected, $payload->toArray());
    }

    public function testToJson(): void
    {
        $creditor = new IpidCreditor();
        $creditor->setName('John Smith');

        $creditorAccount = new IpidCreditorAccount();
        $creditorAccount->setIban('**********************');

        $creditorAgent = new IpidCreditorAgent();
        $creditorAgent->setBic('WESTGB2L');

        $payload = new IpidValidationPayload('GB', $creditor, $creditorAccount, $creditorAgent);

        $json = $payload->toJson();
        $decoded = json_decode($json, true);

        self::assertIsString($json);
        self::assertNotFalse($decoded);
        self::assertEquals('GB', $decoded['country_code']);
        self::assertEquals('John Smith', $decoded['creditor']['name']);
        self::assertEquals('**********************', $decoded['creditor_account']['iban']);
        self::assertEquals('WESTGB2L', $decoded['creditor_agent']['bic']);
    }
}
