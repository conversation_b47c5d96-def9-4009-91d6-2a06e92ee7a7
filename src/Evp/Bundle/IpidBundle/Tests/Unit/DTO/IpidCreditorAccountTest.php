<?php

declare(strict_types=1);

namespace Evp\Bundle\IpidBundle\Tests\Unit\DTO;

use Evp\Bundle\IpidBundle\DTO\IpidCreditorAccount;
use PHPUnit\Framework\TestCase;

final class IpidCreditorAccountTest extends TestCase
{
    private IpidCreditorAccount $creditorAccount;

    protected function setUp(): void
    {
        $this->creditorAccount = new IpidCreditorAccount();
    }

    public function testSetAndGetIban(): void
    {
        $iban = '**********************';

        $result = $this->creditorAccount->setIban($iban);

        self::assertSame($this->creditorAccount, $result);
        self::assertEquals($iban, $this->creditorAccount->getIban());
    }

    public function testSetIbanWithNull(): void
    {
        $this->creditorAccount->setIban('**********************');
        $result = $this->creditorAccount->setIban(null);

        self::assertSame($this->creditorAccount, $result);
        self::assertNull($this->creditorAccount->getIban());
    }

    public function testSetAndGetAccountId(): void
    {
        $accountId = '*********';

        $result = $this->creditorAccount->setAccountId($accountId);

        self::assertSame($this->creditorAccount, $result);
        self::assertEquals($accountId, $this->creditorAccount->getAccountId());
    }

    public function testSetAccountIdWithNull(): void
    {
        $this->creditorAccount->setAccountId('*********');
        $result = $this->creditorAccount->setAccountId(null);

        self::assertSame($this->creditorAccount, $result);
        self::assertNull($this->creditorAccount->getAccountId());
    }

    public function testSetAndGetAccountHolderType(): void
    {
        $accountHolderType = 'individual';

        $result = $this->creditorAccount->setAccountHolderType($accountHolderType);

        self::assertSame($this->creditorAccount, $result);
        self::assertEquals($accountHolderType, $this->creditorAccount->getAccountHolderType());
    }

    public function testSetAccountHolderTypeWithNull(): void
    {
        $this->creditorAccount->setAccountHolderType('individual');
        $result = $this->creditorAccount->setAccountHolderType(null);

        self::assertSame($this->creditorAccount, $result);
        self::assertNull($this->creditorAccount->getAccountHolderType());
    }

    /**
     * @dataProvider hasIbanOrAccountIdDataProvider
     */
    public function testHasIbanOrAccountId(
        ?string $iban,
        ?string $accountId,
        bool $expectedResult
    ): void {
        $this->creditorAccount->setIban($iban)->setAccountId($accountId);

        $result = $this->creditorAccount->hasIbanOrAccountId();

        self::assertEquals($expectedResult, $result);
    }

    public function hasIbanOrAccountIdDataProvider(): array
    {
        return [
            'Has IBAN only' => ['**********************', null, true],
            'Has account ID only' => [null, '*********', true],
            'Has both IBAN and account ID' => ['**********************', '*********', true],
            'Has neither IBAN nor account ID' => [null, null, false],
        ];
    }

    public function testToArrayWithIbanOnly(): void
    {
        $this->creditorAccount->setIban('**********************');

        $result = $this->creditorAccount->toArray();

        $expected = [
            'iban' => '**********************',
        ];

        self::assertEquals($expected, $result);
    }

    public function testToArrayWithAccountIdOnly(): void
    {
        $this->creditorAccount->setAccountId('*********');

        $result = $this->creditorAccount->toArray();

        $expected = [
            'account_id' => '*********',
        ];

        self::assertEquals($expected, $result);
    }

    public function testToArrayPrioritizesIbanOverAccountId(): void
    {
        $this->creditorAccount
            ->setIban('**********************')
            ->setAccountId('*********')
        ;

        $result = $this->creditorAccount->toArray();

        $expected = [
            'iban' => '**********************',
        ];

        self::assertEquals($expected, $result);
        self::assertArrayNotHasKey('account_id', $result);
    }

    public function testToArrayWithIbanAndAccountHolderType(): void
    {
        $this->creditorAccount
            ->setIban('**********************')
            ->setAccountHolderType('individual')
        ;

        $result = $this->creditorAccount->toArray();

        $expected = [
            'iban' => '**********************',
            'account_holder_type' => 'individual',
        ];

        self::assertEquals($expected, $result);
    }

    public function testToArrayWithAccountIdAndAccountHolderType(): void
    {
        $this->creditorAccount
            ->setAccountId('*********')
            ->setAccountHolderType('business')
        ;

        $result = $this->creditorAccount->toArray();

        $expected = [
            'account_id' => '*********',
            'account_holder_type' => 'business',
        ];

        self::assertEquals($expected, $result);
    }

    public function testToArrayWithAllFieldsButIbanTakesPriority(): void
    {
        $this->creditorAccount
            ->setIban('**********************')
            ->setAccountId('*********')
            ->setAccountHolderType('individual')
        ;

        $result = $this->creditorAccount->toArray();

        $expected = [
            'iban' => '**********************',
            'account_holder_type' => 'individual',
        ];

        self::assertEquals($expected, $result);
        self::assertArrayNotHasKey('account_id', $result);
    }

    public function testToArrayWithNoAccountInformation(): void
    {
        $this->creditorAccount->setAccountHolderType('business');

        $result = $this->creditorAccount->toArray();

        $expected = [
            'account_holder_type' => 'business',
        ];

        self::assertEquals($expected, $result);
    }

    public function testToArrayWithNullValues(): void
    {
        $this->creditorAccount
            ->setIban(null)
            ->setAccountId(null)
            ->setAccountHolderType(null)
        ;

        $result = $this->creditorAccount->toArray();

        self::assertEquals([], $result);
    }

    public function testToArrayExcludesNullAccountHolderType(): void
    {
        $this->creditorAccount
            ->setIban('**********************')
            ->setAccountHolderType(null)
        ;

        $result = $this->creditorAccount->toArray();

        $expected = [
            'iban' => '**********************',
        ];

        self::assertEquals($expected, $result);
        self::assertArrayNotHasKey('account_holder_type', $result);
    }

    public function testFluentInterface(): void
    {
        $result = $this->creditorAccount
            ->setIban('**********************')
            ->setAccountId('*********')
            ->setAccountHolderType('individual')
        ;

        self::assertSame($this->creditorAccount, $result);
    }

    public function testInitialState(): void
    {
        $creditorAccount = new IpidCreditorAccount();

        self::assertNull($creditorAccount->getIban());
        self::assertNull($creditorAccount->getAccountId());
        self::assertNull($creditorAccount->getAccountHolderType());
        self::assertFalse($creditorAccount->hasIbanOrAccountId());
        self::assertEquals([], $creditorAccount->toArray());
    }

    /**
     * @dataProvider businessScenarioDataProvider
     */
    public function testBusinessScenarios(
        string $scenario,
        array $setterCalls,
        array $expectedArray,
        bool $expectedHasAccount
    ): void {
        foreach ($setterCalls as $method => $value) {
            $this->creditorAccount->$method($value);
        }

        self::assertEquals($expectedArray, $this->creditorAccount->toArray(), "Failed for scenario: $scenario");
        self::assertEquals($expectedHasAccount, $this->creditorAccount->hasIbanOrAccountId(), "Failed hasIbanOrAccountId for scenario: $scenario");
    }

    public function businessScenarioDataProvider(): array
    {
        return [
            'SEPA IBAN payment' => [
                'scenario' => 'SEPA IBAN payment',
                'setterCalls' => [
                    'setIban' => '**********************',
                    'setAccountHolderType' => 'individual',
                ],
                'expectedArray' => [
                    'iban' => '**********************',
                    'account_holder_type' => 'individual',
                ],
                'expectedHasAccount' => true,
            ],
            'US ACH payment' => [
                'scenario' => 'US ACH payment',
                'setterCalls' => [
                    'setAccountId' => '*********',
                    'setAccountHolderType' => 'business',
                ],
                'expectedArray' => [
                    'account_id' => '*********',
                    'account_holder_type' => 'business',
                ],
                'expectedHasAccount' => true,
            ],
            'Chinese domestic payment' => [
                'scenario' => 'Chinese domestic payment',
                'setterCalls' => [
                    'setAccountId' => '6217000010012345678',
                ],
                'expectedArray' => [
                    'account_id' => '6217000010012345678',
                ],
                'expectedHasAccount' => true,
            ],
            'Brazilian PIX payment' => [
                'scenario' => 'Brazilian PIX payment',
                'setterCalls' => [
                    'setAccountId' => '12345-6',
                ],
                'expectedArray' => [
                    'account_id' => '12345-6',
                ],
                'expectedHasAccount' => true,
            ],
            'Account without currency' => [
                'scenario' => 'Account without currency',
                'setterCalls' => [
                    'setIban' => '***************************',
                ],
                'expectedArray' => [
                    'iban' => '***************************',
                ],
                'expectedHasAccount' => true,
            ],
            'IBAN override account ID' => [
                'scenario' => 'IBAN override account ID',
                'setterCalls' => [
                    'setAccountId' => '*********',
                    'setIban' => '***************************',
                ],
                'expectedArray' => [
                    'iban' => '***************************',
                ],
                'expectedHasAccount' => true,
            ],
            'Empty account' => [
                'scenario' => 'Empty account',
                'setterCalls' => [],
                'expectedArray' => [],
                'expectedHasAccount' => false,
            ],
        ];
    }

    /**
     * @dataProvider accountHolderTypeScenarioDataProvider
     */
    public function testAccountHolderTypeHandling(string $accountHolderType, array $expectedArray): void
    {
        $this->creditorAccount
            ->setAccountId('*********')
            ->setAccountHolderType($accountHolderType)
        ;

        $result = $this->creditorAccount->toArray();

        self::assertEquals($expectedArray, $result);
    }

    public function accountHolderTypeScenarioDataProvider(): array
    {
        return [
            'Individual account holder' => [
                'accountHolderType' => 'individual',
                'expectedArray' => ['account_id' => '*********', 'account_holder_type' => 'individual'],
            ],
            'Business account holder' => [
                'accountHolderType' => 'business',
                'expectedArray' => ['account_id' => '*********', 'account_holder_type' => 'business'],
            ],
            'Corporate account holder' => [
                'accountHolderType' => 'corporate',
                'expectedArray' => ['account_id' => '*********', 'account_holder_type' => 'corporate'],
            ],
        ];
    }
} 