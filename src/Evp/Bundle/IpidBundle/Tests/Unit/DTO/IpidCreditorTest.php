<?php

declare(strict_types=1);

namespace Evp\Bundle\IpidBundle\Tests\Unit\DTO;

use Evp\Bundle\IpidBundle\DTO\IpidCreditor;
use PHPUnit\Framework\TestCase;

final class IpidCreditorTest extends TestCase
{
    private IpidCreditor $creditor;

    protected function setUp(): void
    {
        $this->creditor = new IpidCreditor();
    }

    public function testSetAndGetName(): void
    {
        $name = '<PERSON>';

        $result = $this->creditor->setName($name);

        self::assertSame($this->creditor, $result);
        self::assertEquals($name, $this->creditor->getName());
    }

    public function testSetNameWithNull(): void
    {
        $this->creditor->setName('<PERSON>');
        $result = $this->creditor->setName(null);

        self::assertSame($this->creditor, $result);
        self::assertNull($this->creditor->getName());
    }



    public function testSetAndGetIdentification(): void
    {
        $identification = [
            ['value' => '*********', 'type' => 'passport'],
            ['value' => '*********', 'type' => 'national_id'],
        ];

        $result = $this->creditor->setIdentification($identification);

        self::assertSame($this->creditor, $result);
        self::assertEquals($identification, $this->creditor->getIdentification());
    }

    public function testAddIdentification(): void
    {
        $result = $this->creditor->addIdentification('*********', 'passport');

        self::assertSame($this->creditor, $result);

        $identification = $this->creditor->getIdentification();
        self::assertCount(1, $identification);
        self::assertEquals(['value' => '*********', 'type' => 'passport'], $identification[0]);
    }

    public function testAddMultipleIdentifications(): void
    {
        $this->creditor
            ->addIdentification('*********', 'passport')
            ->addIdentification('*********', 'national_id')
        ;

        $identification = $this->creditor->getIdentification();
        self::assertCount(2, $identification);
        self::assertEquals(['value' => '*********', 'type' => 'passport'], $identification[0]);
        self::assertEquals(['value' => '*********', 'type' => 'national_id'], $identification[1]);
    }

    /**
     * @dataProvider hasNameOrIdentificationDataProvider
     */
    public function testHasNameOrIdentification(
        ?string $name,
        array $identification,
        bool $expectedResult
    ): void {
        $this->creditor->setName($name)->setIdentification($identification);

        $result = $this->creditor->hasNameOrIdentification();

        self::assertEquals($expectedResult, $result);
    }

    public function hasNameOrIdentificationDataProvider(): array
    {
        return [
            'Has name only' => ['John Smith', [], true],
            'Has identification only' => [null, [['value' => '123456', 'type' => 'passport']], true],
            'Has both name and identification' => ['John Smith', [['value' => '123456', 'type' => 'passport']], true],
            'Has neither name nor identification' => [null, [], false],
        ];
    }

    public function testToArrayWithAllFields(): void
    {
        $this->creditor
            ->setName('John Smith')
            ->addIdentification('*********', 'passport')
        ;

        $result = $this->creditor->toArray();

        $expected = [
            'name' => 'John Smith',
            'identification' => [
                ['value' => '*********', 'type' => 'passport'],
            ],
        ];

        self::assertEquals($expected, $result);
    }

    public function testToArrayWithMinimalFields(): void
    {
        $this->creditor->setName('John Smith');

        $result = $this->creditor->toArray();

        $expected = [
            'name' => 'John Smith',
        ];

        self::assertEquals($expected, $result);
    }

    public function testToArrayExcludesNullValues(): void
    {
        $this->creditor->setName('John Smith');

        $result = $this->creditor->toArray();

        $expected = [
            'name' => 'John Smith',
        ];

        self::assertEquals($expected, $result);
    }

    public function testToArrayWithEmptyIdentification(): void
    {
        $this->creditor
            ->setName('John Smith')
            ->setIdentification([])
        ;

        $result = $this->creditor->toArray();

        $expected = [
            'name' => 'John Smith',
        ];

        self::assertEquals($expected, $result);
        self::assertArrayNotHasKey('identification', $result);
    }

    public function testToArrayWithIdentificationOnly(): void
    {
        $this->creditor->addIdentification('*********', 'passport');

        $result = $this->creditor->toArray();

        $expected = [
            'identification' => [
                ['value' => '*********', 'type' => 'passport'],
            ],
        ];

        self::assertEquals($expected, $result);
    }

    public function testToArrayWithMultipleIdentifications(): void
    {
        $this->creditor
            ->setName('John Smith')
            ->addIdentification('*********', 'passport')
            ->addIdentification('*********', 'national_id')
            ->addIdentification('*********', 'driver_license')
        ;

        $result = $this->creditor->toArray();

        $expected = [
            'name' => 'John Smith',
            'identification' => [
                ['value' => '*********', 'type' => 'passport'],
                ['value' => '*********', 'type' => 'national_id'],
                ['value' => '*********', 'type' => 'driver_license'],
            ],
        ];

        self::assertEquals($expected, $result);
    }

    public function testFluentInterface(): void
    {
        $result = $this->creditor
            ->setName('John Smith')
            ->addIdentification('*********', 'passport')
            ->setIdentification([['value' => '*********', 'type' => 'national_id']])
        ;

        self::assertSame($this->creditor, $result);
    }

    public function testInitialState(): void
    {
        $creditor = new IpidCreditor();

        self::assertNull($creditor->getName());
        self::assertEquals([], $creditor->getIdentification());
        self::assertFalse($creditor->hasNameOrIdentification());
    }

    /**
     * @dataProvider businessScenarioDataProvider
     */
    public function testBusinessScenarios(
        string $scenario,
        array $setterCalls,
        array $expectedArray,
        bool $expectedHasName
    ): void {
        foreach ($setterCalls as $method => $value) {
            if ($method === 'addIdentification') {
                foreach ($value as $identification) {
                    $this->creditor->addIdentification($identification['value'], $identification['type']);
                }
            } else {
                $this->creditor->$method($value);
            }
        }

        self::assertEquals($expectedArray, $this->creditor->toArray(), "Failed for scenario: $scenario");
        self::assertEquals($expectedHasName, $this->creditor->hasNameOrIdentification(), "Failed hasNameOrIdentification for scenario: $scenario");
    }

    public function businessScenarioDataProvider(): array
    {
        return [
            'Individual with name only' => [
                'scenario' => 'Individual with name only',
                'setterCalls' => [
                    'setName' => 'John Smith',
                ],
                'expectedArray' => [
                    'name' => 'John Smith',
                ],
                'expectedHasName' => true,
            ],
            'Business entity with identification only' => [
                'scenario' => 'Business entity with identification only',
                'setterCalls' => [
                    'addIdentification' => [
                        ['value' => '*********01', 'type' => 'tax_id'],
                        ['value' => 'REG123456', 'type' => 'registration_number'],
                    ],
                ],
                'expectedArray' => [
                    'identification' => [
                        ['value' => '*********01', 'type' => 'tax_id'],
                        ['value' => 'REG123456', 'type' => 'registration_number'],
                    ],
                ],
                'expectedHasName' => true,
            ],
            'Entity with both name and identification' => [
                'scenario' => 'Entity with both name and identification',
                'setterCalls' => [
                    'setName' => 'ACME Corporation Ltd',
                    'addIdentification' => [
                        ['value' => '*********01', 'type' => 'tax_id'],
                    ],
                ],
                'expectedArray' => [
                    'name' => 'ACME Corporation Ltd',
                    'identification' => [
                        ['value' => '*********01', 'type' => 'tax_id'],
                    ],
                ],
                'expectedHasName' => true,
            ],
        ];
    }
} 