<?php

declare(strict_types=1);

namespace Evp\Bundle\IpidBundle\Tests\Unit\DTO;

use Evp\Bundle\IpidBundle\DTO\IpidCreditorAgent;
use PHPUnit\Framework\TestCase;

final class IpidCreditorAgentTest extends TestCase
{
    private IpidCreditorAgent $creditorAgent;

    protected function setUp(): void
    {
        $this->creditorAgent = new IpidCreditorAgent();
    }

    public function testSetAndGetBic(): void
    {
        $bic = 'DEUTDEFF500';

        $result = $this->creditorAgent->setBic($bic);

        self::assertSame($this->creditorAgent, $result);
        self::assertEquals($bic, $this->creditorAgent->getBic());
    }

    public function testSetBicWithNull(): void
    {
        $this->creditorAgent->setBic('DEUTDEFF500');
        $result = $this->creditorAgent->setBic(null);

        self::assertSame($this->creditorAgent, $result);
        self::assertNull($this->creditorAgent->getBic());
    }

    public function testSetAndGetClearingSystemId(): void
    {
        $clearingSystemId = '*********';

        $result = $this->creditorAgent->setClearingSystemId($clearingSystemId);

        self::assertSame($this->creditorAgent, $result);
        self::assertEquals($clearingSystemId, $this->creditorAgent->getClearingSystemId());
    }

    public function testSetClearingSystemIdWithNull(): void
    {
        $this->creditorAgent->setClearingSystemId('*********');
        $result = $this->creditorAgent->setClearingSystemId(null);

        self::assertSame($this->creditorAgent, $result);
        self::assertNull($this->creditorAgent->getClearingSystemId());
    }



    public function testToArrayWithBicOnly(): void
    {
        $this->creditorAgent->setBic('DEUTDEFF500');

        $result = $this->creditorAgent->toArray();

        $expected = [
            'bic' => 'DEUTDEFF500',
        ];

        self::assertEquals($expected, $result);
    }

    public function testToArrayWithClearingSystemIdOnly(): void
    {
        $this->creditorAgent->setClearingSystemId('*********');

        $result = $this->creditorAgent->toArray();

        $expected = [
            'clearing_system_id' => '*********',
        ];

        self::assertEquals($expected, $result);
    }

    public function testToArrayWithAllFields(): void
    {
        $this->creditorAgent
            ->setBic('DEUTDEFF500')
            ->setClearingSystemId('*********')
        ;

        $result = $this->creditorAgent->toArray();

        $expected = [
            'bic' => 'DEUTDEFF500',
            'clearing_system_id' => '*********',
        ];

        self::assertEquals($expected, $result);
    }

    public function testToArrayExcludesNullValues(): void
    {
        $this->creditorAgent
            ->setBic('DEUTDEFF500')
            ->setClearingSystemId(null)
        ;

        $result = $this->creditorAgent->toArray();

        $expected = [
            'bic' => 'DEUTDEFF500',
        ];

        self::assertEquals($expected, $result);
        self::assertArrayNotHasKey('clearing_system_id', $result);
    }

    public function testToArrayWithEmptyAgent(): void
    {
        $result = $this->creditorAgent->toArray();

        self::assertEquals([], $result);
    }

    public function testFluentInterface(): void
    {
        $result = $this->creditorAgent
            ->setBic('DEUTDEFF500')
            ->setClearingSystemId('*********')
        ;

        self::assertSame($this->creditorAgent, $result);
    }

    public function testInitialState(): void
    {
        $creditorAgent = new IpidCreditorAgent();

        self::assertNull($creditorAgent->getBic());
        self::assertNull($creditorAgent->getClearingSystemId());
        self::assertEquals([], $creditorAgent->toArray());
    }

    /**
     * @dataProvider businessScenarioDataProvider
     */
    public function testBusinessScenarios(
        string $scenario,
        array $setterCalls,
        array $expectedArray
    ): void {
        foreach ($setterCalls as $method => $value) {
            $this->creditorAgent->$method($value);
        }

        self::assertEquals($expectedArray, $this->creditorAgent->toArray(), "Failed for scenario: $scenario");
    }

    public function businessScenarioDataProvider(): array
    {
        return [
            'SEPA payment with BIC' => [
                'scenario' => 'SEPA payment with BIC',
                'setterCalls' => [
                    'setBic' => 'DEUTDEFF500',
                ],
                'expectedArray' => [
                    'bic' => 'DEUTDEFF500',
                ],
            ],
            'US ACH payment with routing number' => [
                'scenario' => 'US ACH payment with routing number',
                'setterCalls' => [
                    'setClearingSystemId' => '*********',
                ],
                'expectedArray' => [
                    'clearing_system_id' => '*********',
                ],
            ],
            'UK Faster Payments with sort code and BIC' => [
                'scenario' => 'UK Faster Payments with sort code and BIC',
                'setterCalls' => [
                    'setClearingSystemId' => '20-20-15',
                    'setBic' => 'BARCGB22',
                ],
                'expectedArray' => [
                    'bic' => 'BARCGB22',
                    'clearing_system_id' => '20-20-15',
                ],
            ],
            'Brazilian bank with clearing system' => [
                'scenario' => 'Brazilian bank with clearing system',
                'setterCalls' => [
                    'setClearingSystemId' => '001',
                ],
                'expectedArray' => [
                    'clearing_system_id' => '001',
                ],
            ],
            'Empty agent' => [
                'scenario' => 'Empty agent',
                'setterCalls' => [],
                'expectedArray' => [],
            ],
        ];
    }

    /**
     * @dataProvider bicFormatDataProvider
     */
    public function testBicFormatHandling(string $bic, array $expectedArray): void
    {
        $this->creditorAgent->setBic($bic);

        $result = $this->creditorAgent->toArray();

        self::assertEquals($expectedArray, $result);
    }

    public function bicFormatDataProvider(): array
    {
        return [
            '8-character BIC' => [
                'bic' => 'DEUTDEFF',
                'expectedArray' => ['bic' => 'DEUTDEFF'],
            ],
            '11-character BIC' => [
                'bic' => 'DEUTDEFF500',
                'expectedArray' => ['bic' => 'DEUTDEFF500'],
            ],
            'US BIC' => [
                'bic' => 'CHASUS33',
                'expectedArray' => ['bic' => 'CHASUS33'],
            ],
            'UK BIC' => [
                'bic' => 'BARCGB22',
                'expectedArray' => ['bic' => 'BARCGB22'],
            ],
            'Singapore BIC' => [
                'bic' => 'DBSSSGSG',
                'expectedArray' => ['bic' => 'DBSSSGSG'],
            ],
        ];
    }

    /**
     * @dataProvider clearingSystemIdDataProvider
     */
    public function testClearingSystemIdHandling(string $clearingSystemId, array $expectedArray): void
    {
        $this->creditorAgent->setClearingSystemId($clearingSystemId);

        $result = $this->creditorAgent->toArray();

        self::assertEquals($expectedArray, $result);
    }

    public function clearingSystemIdDataProvider(): array
    {
        return [
            'US routing number' => [
                'clearingSystemId' => '*********',
                'expectedArray' => ['clearing_system_id' => '*********'],
            ],
            'UK sort code' => [
                'clearingSystemId' => '20-20-15',
                'expectedArray' => ['clearing_system_id' => '20-20-15'],
            ],
            'Brazilian bank code' => [
                'clearingSystemId' => '001',
                'expectedArray' => ['clearing_system_id' => '001'],
            ],
            'Canadian institution number' => [
                'clearingSystemId' => '006',
                'expectedArray' => ['clearing_system_id' => '006'],
            ],
            'Australian BSB' => [
                'clearingSystemId' => '032-001',
                'expectedArray' => ['clearing_system_id' => '032-001'],
            ],
        ];
    }

    public function testComplexCombinationsOfFields(): void
    {
        // Test that various combinations work correctly
        $testCases = [
            ['setBic' => 'DEUTDEFF500', 'setClearingSystemId' => '********'],
            ['setBic' => 'TEST1234'],
            ['setClearingSystemId' => '999999'],
            [],
        ];

        foreach ($testCases as $index => $setterCalls) {
            $agent = new IpidCreditorAgent();

            foreach ($setterCalls as $method => $value) {
                $agent->$method($value);
            }

            $result = $agent->toArray();

            // Verify that the array contains exactly the expected keys
            $expectedKeys = array_map(
                fn($method) => $this->getExpectedArrayKey($method),
                array_keys($setterCalls)
            );

            $resultKeys = array_keys($result);
            sort($expectedKeys);
            sort($resultKeys);

            self::assertEquals(
                $expectedKeys,
                $resultKeys,
                "Test case $index failed: keys mismatch"
            );
        }
    }

    private function getExpectedArrayKey(string $method): string
    {
        $mapping = [
            'setBic' => 'bic',
            'setClearingSystemId' => 'clearing_system_id',
        ];

        return $mapping[$method];
    }
}

