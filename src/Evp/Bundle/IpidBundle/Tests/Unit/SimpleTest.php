<?php

declare(strict_types=1);

namespace Evp\Bundle\IpidBundle\Tests\Unit;

use PHPUnit\Framework\TestCase;

final class SimpleTest extends TestCase
{
    public function testSimpleAssertion(): void
    {
        self::assertTrue(true);
    }

    public function testBasicMath(): void
    {
        self::assertEquals(4, 2 + 2);
    }

    public function testStringComparison(): void
    {
        self::assertEquals('hello', 'hello');
    }
}
