<?php

declare(strict_types=1);

namespace Evp\Bundle\IpidBundle\Tests\Unit\Service;

use Evp\Bundle\BankTransferBundle\Entity\TransferOut;
use Evp\Bundle\BankTransferBundle\Entity\TransferParty\PartyAccountCountry;
use Evp\Bundle\BankTransferBundle\Entity\TransferParty\PartyIban;
use Evp\Bundle\IpidBundle\Service\IpidRequestBuilder;
use PHPUnit\Framework\TestCase;

class IpidRequestBuilderSimplifiedTest extends TestCase
{
    private IpidRequestBuilder $requestBuilder;

    protected function setUp(): void
    {
        $this->requestBuilder = new IpidRequestBuilder();
    }

    public function testBuildRequestForUkCopExample(): void
    {
        // Create transfer matching UK/COP example from documentation
        $transfer = new TransferOut();
        $beneficiary = $this->createMock(PartyAccountCountry::class);
        $beneficiary->expects($this->any())->method('getName')->willReturn('SHERLOCK HOLMES');
        $beneficiary->expects($this->any())->method('getDisplayAccount')->willReturn('********');
        $beneficiary->expects($this->any())->method('getBankCode')->willReturn('112233');
        $beneficiary->expects($this->any())->method('getBic')->willReturn('ABCDGB2L');

        $transfer->setBeneficiary($beneficiary);

        $request = $this->requestBuilder->buildRequest($transfer);
        $requestData = json_decode($request->getJsonPayload(), true);

        // Verify structure matches documentation
        $this->assertEquals('GB', $requestData['country_code']);
        $this->assertEquals('SHERLOCK HOLMES', $requestData['creditor']['name']);
        $this->assertEquals('********', $requestData['creditor_account']['account_id']);
        $this->assertEquals('112233', $requestData['creditor_agent']['clearing_system_id']);
    }

    public function testBuildRequestForEuVopExample(): void
    {
        // Create transfer matching EU/VOP example from documentation
        $transfer = new TransferOut();
        $beneficiary = $this->createMock(PartyIban::class);
        $beneficiary->expects($this->any())->method('getName')->willReturn('Otto Larsen');
        $beneficiary->expects($this->any())->method('getIban')->willReturn('**********************');

        $transfer->setBeneficiary($beneficiary);

        $request = $this->requestBuilder->buildRequest($transfer);
        $requestData = json_decode($request->getJsonPayload(), true);

        // Verify structure matches documentation
        $this->assertEquals('DE', $requestData['country_code']);
        $this->assertEquals('Otto Larsen', $requestData['creditor']['name']);
        $this->assertEquals('**********************', $requestData['creditor_account']['iban']);

        // creditor_agent should not be present if empty
        $this->assertArrayNotHasKey('creditor_agent', $requestData);
    }

    public function testBuildRequestForGlobalExample(): void
    {
        // Create transfer matching Global example from documentation
        $transfer = new TransferOut();
        $beneficiary = $this->createMock(PartyAccountCountry::class);
        $beneficiary->expects($this->any())->method('getName')->willReturn('VIRAT KOHLI');
        $beneficiary->expects($this->any())->method('getDisplayAccount')->willReturn('************');
        $beneficiary->expects($this->any())->method('getBankCode')->willReturn('YESB0000002');
        $beneficiary->expects($this->any())->method('getBic')->willReturn('YESBINBB');

        $transfer->setBeneficiary($beneficiary);

        $request = $this->requestBuilder->buildRequest($transfer);
        $requestData = json_decode($request->getJsonPayload(), true);

        // Verify structure matches documentation
        $this->assertEquals('IN', $requestData['country_code']);
        $this->assertEquals('VIRAT KOHLI', $requestData['creditor']['name']);
        $this->assertEquals('************', $requestData['creditor_account']['account_id']);
        $this->assertEquals('YESB0000002', $requestData['creditor_agent']['clearing_system_id']);
    }

    public function testThrowsExceptionWhenBeneficiaryNameMissing(): void
    {
        $transfer = new TransferOut();
        $beneficiary = $this->createMock(PartyAccountCountry::class);
        $beneficiary->expects($this->any())->method('getName')->willReturn(null);
        $beneficiary->expects($this->any())->method('getDisplayName')->willReturn(null);
        $beneficiary->expects($this->any())->method('getBic')->willReturn('ABCDGB2L'); // Provide BIC for country extraction

        $transfer->setBeneficiary($beneficiary);

        $this->expectException(\InvalidArgumentException::class);
        $this->expectExceptionMessage('Beneficiary name is required for iPiD validation');

        $this->requestBuilder->buildRequest($transfer);
    }

    public function testThrowsExceptionWhenAccountMissing(): void
    {
        $transfer = new TransferOut();
        $beneficiary = $this->createMock(PartyAccountCountry::class);
        $beneficiary->expects($this->any())->method('getName')->willReturn('Test Name');
        $beneficiary->expects($this->any())->method('getDisplayAccount')->willReturn(null);
        $beneficiary->expects($this->any())->method('getBic')->willReturn('ABCDGB2L'); // Provide BIC for country extraction

        $transfer->setBeneficiary($beneficiary);

        $this->expectException(\InvalidArgumentException::class);
        $this->expectExceptionMessage('Beneficiary account number is required for iPiD validation');

        $this->requestBuilder->buildRequest($transfer);
    }
}
