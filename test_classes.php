<?php

require_once 'vendor/autoload.php';

echo "Testing class loading...\n";

try {
    echo "1. Testing IpidValidationService class exists: ";
    var_dump(class_exists('Evp\Bundle\IpidBundle\Service\IpidValidationService'));
    
    echo "2. Testing IpidCountryRegistry class exists: ";
    var_dump(class_exists('Evp\Bundle\IpidBundle\Service\IpidCountryRegistry'));
    
    echo "3. Testing IpidRequestBuilder class exists: ";
    var_dump(class_exists('Evp\Bundle\IpidBundle\Service\IpidRequestBuilder'));
    
    echo "4. Testing IpidValidationResult class exists: ";
    var_dump(class_exists('Evp\Bundle\IpidBundle\Service\IpidValidationResult'));
    
    echo "5. Testing PHPUnit TestCase class exists: ";
    var_dump(class_exists('PHPUnit\Framework\TestCase'));
    
    echo "All classes loaded successfully!\n";
    
} catch (Exception $e) {
    echo "Error: " . $e->getMessage() . "\n";
}
